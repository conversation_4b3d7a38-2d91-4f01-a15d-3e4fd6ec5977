# Mac系统启动服务器说明

## 快速启动

已为您创建了Mac版本的启动脚本 `start_server.sh`，使用方法：

### 方法1: 双击运行（推荐）
1. 在Finder中找到 `start_server.sh` 文件
2. 右键点击文件，选择"打开方式" → "终端"
3. 如果提示权限问题，先执行方法2设置权限

### 方法2: 终端命令行运行
```bash
# 1. 打开终端（Terminal）
# 2. 进入项目目录
cd /path/to/your/project

# 3. 设置执行权限（只需要执行一次）
chmod +x start_server.sh

# 4. 运行脚本
./start_server.sh
```

### 方法3: 直接用bash运行（无需设置权限）
```bash
bash start_server.sh
```

## 访问地址

服务器启动后，在浏览器中访问：

- **主页**: http://localhost:8080
- **HG数据页面**: http://localhost:8080/HG.html  
- **OB数据页面**: http://localhost:8080/OB.html

## 停止服务器

在终端窗口中按 `Ctrl + C` 即可停止服务器

## 脚本功能

`start_server.sh` 脚本会自动：

✅ 检查Node.js是否已安装  
✅ 检查npm版本  
✅ 查找服务器文件（app.js或server.js）  
✅ 自动安装依赖（如果需要）  
✅ 启动服务器  
✅ 显示访问地址  

## 故障排除

### 如果提示"未找到Node.js"
请先安装Node.js：
- 访问 https://nodejs.org/ 下载安装
- 或使用Homebrew: `brew install node`

### 如果提示权限被拒绝
```bash
chmod +x start_server.sh
```

### 如果端口8080被占用
脚本会自动处理，或者您可以修改服务器文件中的端口设置

## 与Windows版本的区别

| Windows (start_server.bat) | Mac (start_server.sh) |
|----------------------------|----------------------|
| 双击直接运行 | 需要设置执行权限或用bash运行 |
| 自动关闭之前的Node进程 | 手动按Ctrl+C停止 |
| CMD窗口 | Terminal终端 |

## 开发者选项

如果您是开发者，也可以直接使用：
```bash
node app.js
# 或
node server.js
```
