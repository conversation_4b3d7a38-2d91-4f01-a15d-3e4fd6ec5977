const express = require('express');
const axios = require('axios');
const path = require('path');
const xml2js = require('xml2js');

const app = express();
const PORT = process.env.PORT || 8080;

// 默认API URL
const DEFAULT_API_URL = '';

// 设置视图引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// 设置静态文件目录
app.use(express.static(path.join(__dirname, 'public')));

// 处理favicon.ico请求
app.get('/favicon.ico', (req, res) => {
  res.status(204).end(); // 返回204 No Content状态码
});

// 添加JSON和urlencoded解析中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 首页路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 看水监控系统路由
app.get('/jiankong', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'jiankong.html'));
});

// 数据表格页面路由
app.get('/datatable', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'datatable.html'));
});

// 直接API测试页面路由
app.get('/directapi', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'directapi.html'));
});

// XML转JSON查看器路由
app.get('/xmltojson', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'xmltojson.html'));
});

// OB数据页面路由
app.get('/OB', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'OB.html'));
});

// HG数据页面路由
app.get('/HG', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'HG.html'));
});

// 处理自定义POST数据的接口
app.post('/custom-game-data', async (req, res) => {
  try {
    const { postData, apiUrl } = req.body;
    if (!postData) {
      return res.status(400).json({ error: '缺少POST数据' });
    }

    if (!apiUrl) {
      return res.status(400).json({ error: '缺少API URL' });
    }

    // 验证URL格式
    try {
      new URL(apiUrl);
    } catch (e) {
      return res.status(400).json({ error: '无效的URL格式' });
    }
    
    // 解析提交的POST数据为对象
    const formData = new URLSearchParams(postData);
    
    // 确保时间戳是最新的
    if (formData.has('ts')) {
      formData.set('ts', Date.now().toString());
    }
    
    try {
      const response = await axios.post(apiUrl, formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        responseType: 'text',  // 确保获取原始文本响应
        timeout: 10000 // 设置超时为10秒
      });
      
      // 检查响应内容是否有效
      if (!response.data) {
        return res.status(500).json({ error: '服务器返回的数据为空' });
      }
      
      // 修正检查逻辑，允许不同的XML格式
      // 某些响应可能不包含<serverresponse>标签，但仍然是有效的XML
      if (!response.data.includes('<') || !response.data.includes('>')) {
        return res.status(500).json({ error: '服务器返回的数据不是XML格式' });
      }
      
      // 返回原始XML响应数据，不再转换为JSON
      return res.set('Content-Type', 'text/xml; charset=utf-8').send(response.data);
    } catch (requestError) {
      return res.status(500).json({ 
        error: '请求远程API失败', 
        message: requestError.message,
        code: requestError.code
      });
    }
  } catch (error) {
    return res.status(500).json({ error: '获取数据失败', message: error.message });
  }
});

// 旧的游戏数据接口保留，但使用默认参数
app.get('/game-data', async (req, res) => {
  try {
    // 检查默认API URL是否已设置
    if (!DEFAULT_API_URL) {
      return res.status(400).json({ error: '默认API URL未设置，请使用custom-game-data接口' });
    }
    
    const postData = {
      uid: 'ne4ko3ewqm37418875l5535514b1',
      ver: '2025-05-07-fixBug_90',
      langx: 'zh-cn',
      p: 'get_game_list',
      p3type: '',
      date: '',
      gtype: 'ft',
      showtype: 'live',
      rtype: 'rb',
      ltype: '3',
      filter: '',
      cupFantasy: 'N',
      sorttype: 'L',
      specialClick: '',
      isFantasy: 'N',
      ts: Date.now()
    };

    // 转换为表单格式
    const formData = new URLSearchParams();
    for (const key in postData) {
      formData.append(key, postData[key]);
    }
    
    const response = await axios.post(DEFAULT_API_URL, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      responseType: 'text',  // 确保获取原始文本响应
      timeout: 10000 // 设置超时为10秒
    });

    // 处理响应数据
    try {
      // 使用xml2js解析XML响应
      const parser = new xml2js.Parser({
        explicitArray: false,
        mergeAttrs: false,
        explicitRoot: true,
        attrkey: '$',
        charkey: '_'
      });
      
      // 解析XML
      const result = await new Promise((resolve, reject) => {
        parser.parseString(response.data, (err, result) => {
          if (err) reject(err);
          else resolve(result);
        });
      });
      
      // 返回解析后的数据
      res.json(result);
    } catch (parseError) {
      // 如果解析失败，返回原始响应
      res.json({ 
        error: '无法解析XML响应',
        raw_response: response.data
      });
    }
  } catch (error) {
    res.status(500).json({ error: '获取数据失败', message: error.message });
  }
});

// 添加额外的测试路由，确认服务器工作正常
app.get('/test', (req, res) => {
  res.json({ status: 'ok', message: '服务器正常工作' });
});

// 添加接收任何类型POST请求的通用路由（用于调试）
app.post('/debug-post', async (req, res) => {
  try {
    const { postData, apiUrl } = req.body;
    
    if (!postData || !apiUrl) {
      return res.json({ 
        received: true, 
        body: req.body,
        message: '已接收数据，但未执行远程API请求（缺少postData或apiUrl）' 
      });
    }
    
    // 验证URL格式
    let targetUrl;
    try {
      targetUrl = new URL(apiUrl);
    } catch (e) {
      return res.json({ 
        received: true, 
        body: req.body,
        error: '无效的URL格式',
        message: '已接收数据，但未执行远程API请求（URL格式无效）'
      });
    }
    
    // 解析提交的POST数据
    const formData = new URLSearchParams(postData);
    
    // 确保时间戳是最新的
    if (formData.has('ts')) {
      formData.set('ts', Date.now().toString());
    }
    
    // 直接向目标API发送请求
    const response = await axios.post(apiUrl, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      timeout: 10000 // 设置超时为10秒
    });
    
    // 返回API响应和请求信息
    return res.json({
      success: true,
      request: {
        url: apiUrl,
        data: formData.toString()
      },
      response: response.data
    });
    
  } catch (error) {
    return res.status(500).json({ 
      error: '请求失败', 
      message: error.message,
      received_data: req.body
    });
  }
});

// 处理直接访问API的接口
app.post('/direct-api-access', async (req, res) => {
  try {
    const { postData, apiUrl } = req.body;
    if (!postData) {
      return res.status(400).json({ error: '缺少POST数据' });
    }

    if (!apiUrl) {
      return res.status(400).json({ error: '缺少API URL' });
    }

    // 验证URL格式
    try {
      new URL(apiUrl);
    } catch (e) {
      return res.status(400).json({ error: '无效的URL格式' });
    }
    
    // 解析提交的POST数据为对象
    const formData = new URLSearchParams(postData);
    
    // 确保时间戳是最新的
    if (formData.has('ts')) {
      formData.set('ts', Date.now().toString());
    }
    
    try {
      const response = await axios.post(apiUrl, formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        responseType: 'text',  // 确保获取原始文本响应
        timeout: 10000 // 设置超时为10秒
      });
      
      // 返回原始API响应
      return res.json({
        success: true,
        status: response.status,
        headers: response.headers,
        raw_data: response.data
      });
    } catch (requestError) {
      return res.status(500).json({ 
        error: '请求远程API失败', 
        message: requestError.message,
        code: requestError.code
      });
    }
  } catch (error) {
    return res.status(500).json({ error: '处理请求失败', message: error.message });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
}); 