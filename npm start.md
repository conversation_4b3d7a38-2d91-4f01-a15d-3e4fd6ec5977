# Node皇冠服务器

足球赛事数据接口服务器，支持获取实时赛事数据并展示。

## 功能特点

- 实时获取足球赛事数据
- 支持自定义API请求参数
- 自动刷新数据
- 比分变化提示音效

## 快速启动

### 首次运行

如果是首次运行项目，请使用：

1. 双击运行 `install_and_start.bat` 文件（这将安装依赖并启动服务器）
2. 在浏览器中访问 http://localhost:8080

### 后续启动

已经安装过依赖后，只需：

1. 双击运行 `start_server.bat` 文件
2. 在浏览器中访问 http://localhost:8080

## 可用页面

- 首页: http://localhost:8080/
- 数据表格页面: http://localhost:8080/datatable
- 直接API测试: http://localhost:8080/directapi
- XML转JSON查看器: http://localhost:8080/xmltojson

## 手动启动

如果需要手动启动服务器，可以使用以下命令：

```bash
# 安装依赖（首次运行）
npm install

# 启动服务器
node app.js
```

## 系统要求

- Node.js 14.0+
- 现代浏览器 (Chrome, Firefox, Edge等)

## 声音设置

- 系统默认开启比分变化提示音
- 可以通过表格页面右上角的声音按钮控制开关

## 项目结构

```
/
├── app.js              # 主应用文件
├── package.json        # 项目配置和依赖
├── views/              # 视图模板
│   └── index.ejs       # 主页面
└── public/             # 静态资源
    ├── css/
    │   └── style.css   # 样式表
    └── js/
        └── main.js     # 前端JavaScript
``` 