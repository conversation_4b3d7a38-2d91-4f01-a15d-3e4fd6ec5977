<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OB滚球页面监控</title>

  <!-- 引入pako库用于GZIP解压缩 -->
  <script src="https://cdn.jsdelivr.net/npm/pako@2.1.0/dist/pako.min.js" onload="console.log('pako库加载完成')" onerror="console.error('pako库加载失败')"></script>
  <!-- 引入CryptoJS库用于加密 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 10px;
      margin: 0;
      line-height: 1.4;
    }
    h1 {
      color: #333;
      border-bottom: 1px solid #d0d0d0;
      padding-bottom: 8px;
      font-size: 18px;
      font-weight: normal;
      margin-top: 10px;
      margin-bottom: 10px;
    }
    .container {
      background-color: white;
      border: 1px solid #d0d0d0;
      border-radius: 0;
      padding: 10px;
      margin-top: 10px;
      width: 100%;
      box-sizing: border-box;
    }
    button {
      background-color: #f2f2f2;
      color: #333;
      border: 1px solid #d0d0d0;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      margin-right: 10px;
    }
    button:hover {
      background-color: #e6e6e6;
    }
    .status {
      margin-top: 20px;
      padding: 10px;
      border-radius: 4px;
    }
    .success {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
    }
    .error {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
    }
    code {
      background-color: #f0f0f0;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: Consolas, monospace;
    }
    .endpoint {
      margin-bottom: 15px;
      border-left: 3px solid #007bff;
      padding-left: 10px;
      word-break: break-all;
    }
    .method-tag {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 3px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      margin-right: 8px;
      background-color: #007bff;
    }
    .url-input {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-family: Consolas, monospace;
      box-sizing: border-box;
    }
    .json-input {
      width: 100%;
      height: 120px;
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-family: Consolas, monospace;
      box-sizing: border-box;
      resize: vertical;
    }
    .form-group {
      margin-bottom: 15px;
    }
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      border-bottom: 1px solid #d0d0d0;
      padding-bottom: 10px;
    }
    .method-selector {
      margin-bottom: 15px;
    }
    .method-selector label {
      margin-right: 15px;
      font-weight: bold;
    }
    
    /* OB表格样式 - Excel风格 */
    .table-container {
      margin-top: 10px;
      overflow-x: auto;
      width: 100%;
      padding: 0;
      border: 1px solid #ccc;
    }
    .ob-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 12px;
      table-layout: fixed;
      background-color: white;
    }
    .ob-table th,
    .ob-table td {
      border: 1px solid #ccc;
      padding: 4px 6px;
      text-align: left;
      vertical-align: middle;
      white-space: normal;
      word-break: break-word;
      overflow: visible;
      text-overflow: clip;
    }
    .ob-table th {
      background-color: #f0f0f0;
      color: #333;
      font-weight: 600;
      position: sticky;
      top: 0;
      z-index: 10;
      text-align: center;
      padding: 6px 4px;
      font-size: 12px;
    }
    /* 设置各列宽度 */
    .ob-table th:nth-child(1), .ob-table td:nth-child(1) { width: 80px; } /* 联赛 */
    .ob-table th:nth-child(2), .ob-table td:nth-child(2) { width: 70px; } /* 时间 */
    .ob-table th:nth-child(3), .ob-table td:nth-child(3) { width: 100px; } /* 主队 */
    .ob-table th:nth-child(4), .ob-table td:nth-child(4) { width: 40px; } /* 比分 */
    .ob-table th:nth-child(5), .ob-table td:nth-child(5) { width: 100px; } /* 客队 */
    .ob-table th:nth-child(6), .ob-table td:nth-child(6) { width: 120px; } /* 全场让球 */
    .ob-table th:nth-child(7), .ob-table td:nth-child(7) { width: 120px; } /* 全场大小 */
    .ob-table th:nth-child(8), .ob-table td:nth-child(8) { width: 120px; } /* 半场让球 */
    .ob-table th:nth-child(9), .ob-table td:nth-child(9) { width: 120px; } /* 半场大小 */
    .ob-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .ob-table tr:nth-child(odd) {
      background-color: #ffffff;
    }
    .ob-table tr:hover {
      background-color: #e8f5fe;
    }
    .odds-cell {
      font-size: 11px;
      line-height: 1.4;
      word-break: break-all;
      color: #333;
    }
    .refresh-btn {
      margin-bottom: 5px;
      padding: 3px 8px;
      font-size: 12px;
      background-color: #f2f2f2;
      color: #333;
      border: 1px solid #d0d0d0;
    }
    .refresh-btn:hover {
      background-color: #e6e6e6;
    }
    .match-count {
      background-color: #f2f2f2;
      color: #333;
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 11px;
      font-weight: bold;
      margin-left: 5px;
      border: 1px solid #d0d0d0;
    }
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 12px;
    }
    .status-cell {
      position: relative;
    }
    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 5px;
      border: 1px solid #d0d0d0;
    }
    .status-live {
      background-color: #e6e6e6;
    }
    .status-upcoming {
      background-color: #e6e6e6;
    }
    .status-finished {
      background-color: #e6e6e6;
    }
    .league-badge {
      background-color: transparent;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 12px;
      color: #333;
    }
    /* 盘口样式 */
    .handicap-odds {
      display: block;
      padding: 4px;
      margin-bottom: 4px;
      background-color: transparent;
      border-radius: 0;
      border-bottom: 1px dotted #d0d0d0;
    }
    
    .size-odds {
      display: block;
      padding: 4px;
      margin-bottom: 4px;
      background-color: transparent;
      border-radius: 0;
      border-bottom: 1px dotted #d0d0d0;
    }
    
    /* 响应式设计 */
    @media screen and (max-width: 1200px) {
      body {
        padding: 10px;
        max-width: 100%;
      }
      
      .ob-table {
        font-size: 13px;
        min-width: 1400px;
      }
      
      .ob-table th,
      .ob-table td {
        padding: 8px 6px;
      }
      
      .odds-cell {
        min-width: 200px;
        font-size: 12px;
      }
    }
    
    @media screen and (max-width: 992px) {
      .ob-table {
        font-size: 12px;
        min-width: 1400px;
      }
      
      .ob-table th,
      .ob-table td {
        padding: 6px 4px;
      }
      
      .odds-cell {
        min-width: 200px;
        font-size: 12px;
        line-height: 1.6;
      }
    }
    
    @media screen and (max-width: 768px) {
      .odds-cell {
        min-width: 200px;
        font-size: 11px;
        line-height: 1.6;
      }
    }
    
    /* 在非常小的屏幕上隐藏部分列 */
    @media screen and (max-width: 600px) {
      .ob-table th:nth-child(8), .ob-table td:nth-child(8),
      .ob-table th:nth-child(9), .ob-table td:nth-child(9) {
        display: none;
      }
    }
    
    /* 排序图标样式 */
    .sort-icon {
      display: inline-block;
      margin-left: 5px;
      font-size: 10px;
      color: #666;
      transition: all 0.2s;
    }
    
    th:hover .sort-icon {
      color: #333;
    }
    
    /* 已排序的列标题样式 */
    th.sorted {
      background-color: #e6e6e6;
    }
    
    /* 添加表头悬停效果 */
    .ob-table th {
      transition: background-color 0.2s;
    }
    
    .ob-table th:hover {
      background-color: #e6e6e6;
    }
    .auto-refresh-container {
      display: flex;
      align-items: center;
    }
    
    #toggle-refresh.active {
      background-color: #e6e6e6;
      color: #333;
      border-color: #999;
    }
    
    #refresh-interval {
      width: 80px;
      padding: 4px;
      border: 1px solid #d0d0d0;
      border-radius: 4px;
      font-size: 13px;
    }
    
    .refresh-status {
      font-size: 12px;
      color: #666;
      margin-left: 10px;
      display: flex;
      align-items: center;
    }
    
    .refresh-status.active {
      color: #28a745;
    }
    
    .loading-spinner {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-right: 5px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #3498db;
      border-radius: 50%;
      animation: spin 1.5s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>OB 数据表格</h1>
  </div>
  
  <div class="container">
    <div class="method-selector">
      <label>请求方法:</label>
      <input type="radio" id="postMethod" name="method" value="POST" checked>
      <label for="postMethod">POST</label>
    </div>
    
    <div class="form-group">
      <label for="apiUrl">请求URL:</label>
      <input type="text" id="apiUrl" class="url-input" value="https://api.dl9qfnzr.com/yewu11/v1/w/structureMatchesPB?t=1747483352030">
      <div style="display: flex; justify-content: flex-end; margin-top: 5px;">
        <button onclick="resetToDefault('apiUrl')" style="background-color: #f8f9fa; color: #666; font-size: 12px; padding: 3px 8px; border: 1px solid #ccc; border-radius: 3px; cursor: pointer; margin-right: 10px;">恢复默认</button>
        <span id="apiUrlStatus" style="font-size: 12px; color: #28a745; display: none;">已保存</span>
      </div>
    </div>
    
    <div class="form-group">
      <label for="requestBody">请求体 (JSON):</label>
      <textarea id="requestBody" class="json-input">{"cuid":"523963395904882753","sort":1,"tid":"","apiType":1,"euid":"30002","cpn":1,"cps":500}</textarea>
      <div style="display: flex; justify-content: flex-end; margin-top: 5px;">
        <button onclick="resetToDefault('requestBody')" style="background-color: #f8f9fa; color: #666; font-size: 12px; padding: 3px 8px; border: 1px solid #ccc; border-radius: 3px; cursor: pointer; margin-right: 10px;">恢复默认</button>
        <span id="requestBodyStatus" style="font-size: 12px; color: #28a745; display: none;">已保存</span>
      </div>
    </div>
    
    <div class="form-group">
      <label for="requestId">请求ID (RequestId):</label>
      <input type="text" id="requestId" class="url-input" value="5ccf745594240ce629e89b41d7bee9665bdb12a1">
      <div style="display: flex; justify-content: flex-end; margin-top: 5px;">
        <button onclick="resetToDefault('requestId')" style="background-color: #f8f9fa; color: #666; font-size: 12px; padding: 3px 8px; border: 1px solid #ccc; border-radius: 3px; cursor: pointer; margin-right: 10px;">恢复默认</button>
        <span id="requestIdStatus" style="font-size: 12px; color: #28a745; display: none;">已保存</span>
      </div>
    </div>
    
    <div class="header">
      <div>
        <button id="sendRequest">发送请求</button>
        <button id="showRawData" style="background-color: #f8f9fa; color: #333; border: 1px solid #ccc;">显示原始数据</button>
      </div>
      <div id="status" class="status" style="display: none; margin: 0;"></div>
    </div>
  </div>

  <script>
    /**
     * OB数据转换模块
     * 负责将OB数据转换为统一的HG格式
     */

    // 添加一个初始化标志，确保转换函数已准备好
    window.obConverterReady = true;

    // 添加数据缓存，避免重复处理相同数据
    let dataCache = {
      lastProcessedData: null,
      lastResult: null,
      timestamp: 0
    };

    /**
     * 将OB JSON格式数据转换为HG格式
     * @param {Object} jsonData - OB JSON格式数据
     * @returns {Object} - 转换后的HG格式数据
     */
    function convertObDataToHgFormat(jsonData) {
      // 创建HG格式的数据结构
      const hgData = {
        标识: 'OB',
        matches: []
      };

      try {
        // 检查是否为重复数据，如果是则直接返回缓存结果
        // 只有在数据相同且缓存时间不超过30秒时才使用缓存
        const currentTime = Date.now();
        const cacheAge = currentTime - dataCache.timestamp;

        if (dataCache.lastProcessedData &&
            JSON.stringify(dataCache.lastProcessedData) === JSON.stringify(jsonData) &&
            dataCache.lastResult &&
            cacheAge < 30000) { // 缓存有效期30秒
          console.log('检测到重复数据，使用缓存结果，缓存年龄:', Math.round(cacheAge/1000), '秒');
          return dataCache.lastResult;
        }

        // 开始转换数据，不输出日志

        // 检查输入数据是否为空
        if (!jsonData) {
          console.warn('转换函数收到空数据');
          return hgData;
        }

        // 检查API返回的错误信息
        if (jsonData.code && jsonData.code !== '0' && jsonData.code !== '0000000') {
          // 处理各种错误码
          if (jsonData.code === '0401013' && jsonData.msg === '账户信息已过期,请重新登录') {
            console.warn('OB账户信息已过期');
          } else if (jsonData.code === '0401038' && jsonData.msg === '当前访问人数过多，请稍后再试') {
            console.warn('OB服务器繁忙: 当前访问人数过多，请稍后再试');
          } else {
            console.warn(`OB API错误: ${jsonData.code} - ${jsonData.msg || '未知错误'}`);
          }

          // 如果有缓存数据，使用缓存数据
          if (dataCache.lastResult && Object.keys(dataCache.lastResult).length > 0 &&
              dataCache.lastResult.matches && dataCache.lastResult.matches.length > 0) {
            console.log('使用缓存数据显示');
            return dataCache.lastResult;
          }

          return hgData;
        }

        // 检查数据结构是否有效
        if (!jsonData.data) {
          console.warn('数据结构无效: 缺少data字段');

          // 如果有缓存数据，使用缓存数据
          if (dataCache.lastResult && Object.keys(dataCache.lastResult).length > 0 &&
              dataCache.lastResult.matches && dataCache.lastResult.matches.length > 0) {
            console.log('使用缓存数据显示');
            return dataCache.lastResult;
          }

          return hgData;
        }

        // 尝试找到数据数组
        let matchArray = null;

        try {
        if (Array.isArray(jsonData.data)) {
          // 标准格式
            console.log('找到标准格式数据数组，长度:', jsonData.data.length);
          matchArray = jsonData.data;
        } else if (Array.isArray(jsonData)) {
          // 如果整个对象就是一个数组
            console.log('输入数据本身是数组，长度:', jsonData.length);
          matchArray = jsonData;
          } else if (typeof jsonData.data === 'string') {
            // 处理字符串格式的data字段
            try {
              // 尝试解析JSON字符串
              const parsedData = JSON.parse(jsonData.data);
              if (Array.isArray(parsedData)) {
                console.log('解析data字段中的JSON字符串成功，长度:', parsedData.length);
                matchArray = parsedData;
              } else if (parsedData && Array.isArray(parsedData.data)) {
                console.log('解析data字段中的嵌套JSON字符串成功，长度:', parsedData.data.length);
                matchArray = parsedData.data;
              }
            } catch (e) {
              console.warn('解析data字段中的JSON字符串失败:', e.message);
            }
        } else if (jsonData.data && typeof jsonData.data === 'object' && !Array.isArray(jsonData.data)) {
          // 如果data是一个对象，尝试找到其中的数组属性
          for (const key in jsonData.data) {
            if (Array.isArray(jsonData.data[key])) {
                console.log(`找到data.${key}数组，长度:`, jsonData.data[key].length);
              matchArray = jsonData.data[key];
              break;
            }
          }
        } else {
          // 尝试在顶层对象中找到任何数组
          for (const key in jsonData) {
            if (Array.isArray(jsonData[key])) {
                console.log(`找到顶层${key}数组，长度:`, jsonData[key].length);
              matchArray = jsonData[key];
              break;
            }
          }
          }
        } catch (e) {
          console.error('查找数据数组时出错:', e);
        }

        if (!matchArray || matchArray.length === 0) {
          console.warn('未找到有效的比赛数据数组');
          return hgData;
        }

        console.log('成功找到比赛数据数组，包含', matchArray.length, '场比赛');

        // 遍历所有赛事
        matchArray.forEach((match, index) => {
          try {
            // 提取比分信息，通常在msc中的S0字段 (格式如 "S0|0:0")
            let score = "0-0";
            if (Array.isArray(match.msc)) {
              const scoreData = match.msc.find(s => s && typeof s === 'string' && s.startsWith("S0|"));
              if (scoreData) {
                const scoreParts = scoreData.split("|")[1].split(":");
                if (scoreParts.length === 2) {
                  score = `${scoreParts[0]}-${scoreParts[1]}`;
                }
              }
            }

            // 处理比赛时间
            const dateTime = formatDateTime(match.mgt);

            // 提取赔率信息
            const odds = extractOdds(match);

            // 创建比赛对象
            const convertedMatch = {
              league: match.tn || '',
              dateTime: dateTime,
              teams: {
                home: match.mhn || '',
                away: match.man || ''
              },
              score: score,
              fullTimeHandicap: odds.fullTimeHandicapList || [],
              fullTimeOverUnder: odds.fullTimeOverUnderList || [],
              halfTimeHandicap: odds.halfTimeHandicapList || [],
              halfTimeOverUnder: odds.halfTimeOverUnderList || []
            };

            // 将比赛添加到结果数组
            hgData.matches.push(convertedMatch);
          } catch (error) {
            console.error('处理比赛数据时出错:', error);
          }
        });

        // 在返回前检查是否成功转换了数据
        if (hgData.matches.length > 0) {
          console.log('成功转换', hgData.matches.length, '场比赛数据');

          // 更新数据缓存
          dataCache.lastProcessedData = JSON.parse(JSON.stringify(jsonData));
          dataCache.lastResult = JSON.parse(JSON.stringify(hgData));
          dataCache.timestamp = Date.now(); // 更新缓存时间戳
        } else {
          console.warn('未能成功转换任何比赛数据');
        }

        return hgData;
      } catch (error) {
        console.error('转换数据时出错:', error);
        // 返回空结果，但确保结构正确
        return { 标识: 'OB', matches: [] };
      } finally {
        // 确保转换函数已准备好接收下一次调用
        window.obConverterReady = true;
      }
    }

    /**
     * 格式化比赛时间
     * @param {string|number} timestamp - 时间戳
     * @returns {string} - 格式化后的时间 "MM-DD HH:MM"
     */
    function formatDateTime(timestamp) {
      if (!timestamp) return '';

      try {
        // 将时间戳转换为日期对象
        // OB API返回的时间戳通常是秒级的，需要转换为毫秒级
        let timestampMs = parseInt(timestamp);

        // 如果时间戳是秒级的（长度为10位左右），转换为毫秒级
        if (timestampMs.toString().length <= 10) {
          timestampMs = timestampMs * 1000;
        }

        const date = new Date(timestampMs);

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '';
        }

        // 格式化月和日
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');

        // 格式化小时和分钟
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');

        return `${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '';
      }
    }

    /**
     * 格式化比赛状态
     * @param {Object} match - 比赛数据
     * @returns {string} - 格式化后的比赛状态
     */
    function formatMatchStatus(match) {
      try {
        // 直接返回原始状态值，不进行格式化
        return match.mlet || "未知状态";
      } catch (error) {
        return "未知状态";
      }
    }

    /**
     * 提取比赛赔率
     * @param {Object} match - 比赛数据
     * @returns {Object} - 格式化后的赔率数据
     */
    function extractOdds(match) {
      try {
        // 初始化盘口数据
        const odds = {
          fullTimeHandicapList: [],
          fullTimeOverUnderList: [],
          halfTimeHandicapList: [],
          halfTimeOverUnderList: []
        };

        try {
          // 处理hpsData - 提取赔率数据（可能有多个盘口）
          if (match.hpsData && Array.isArray(match.hpsData)) {
            for (const hpsGroup of match.hpsData) {
              if (!hpsGroup.hps || !Array.isArray(hpsGroup.hps)) continue;

              for (const hp of hpsGroup.hps) {
                if (!hp.hpid) continue;

                // 根据盘口类型处理
                switch (hp.hpid) {
                  case "4": // 全场让球
                    if (hp.hl) {
                      const ftHandicap = formatHandicap(hp);
                      if (ftHandicap !== '无盘口') {
                        // 添加优先级信息
                        const priority = hp.hl.hn || 999;
                        odds.fullTimeHandicapList.push({ odds: ftHandicap, priority: priority });
                      }
                    }
                    break;
                  case "2": // 全场大小
                    if (hp.hl) {
                      const ftOverUnder = formatOverUnder(hp);
                      if (ftOverUnder !== '无盘口') {
                        // 添加优先级信息
                        const priority = hp.hl.hn || 999;
                        odds.fullTimeOverUnderList.push({ odds: ftOverUnder, priority: priority });
                      }
                    }
                    break;
                  case "19": // 半场让球
                    if (hp.hl) {
                      const htHandicap = formatHandicap(hp);
                      if (htHandicap !== '无盘口') {
                        // 添加优先级信息
                        const priority = hp.hl.hn || 999;
                        odds.halfTimeHandicapList.push({ odds: htHandicap, priority: priority });
                      }
                    }
                    break;
                  case "18": // 半场大小
                    if (hp.hl) {
                      const htOverUnder = formatOverUnder(hp);
                      if (htOverUnder !== '无盘口') {
                        // 添加优先级信息
                        const priority = hp.hl.hn || 999;
                        odds.halfTimeOverUnderList.push({ odds: htOverUnder, priority: priority });
                      }
                    }
                    break;
                }
              }
            }
          }

          // 处理hpsAdd - 提取副盘口数据（可能有多个）
          if (match.hpsData && Array.isArray(match.hpsData)) {
            for (const hpsGroup of match.hpsData) {
              if (!hpsGroup.hpsAdd || !Array.isArray(hpsGroup.hpsAdd)) continue;

              for (const hpadd of hpsGroup.hpsAdd) {
                if (!hpadd.hpid || !hpadd.hl || !Array.isArray(hpadd.hl)) continue;

                // 处理每个副盘口
                for (const hl of hpadd.hl) {
                  if (!hl) continue;

                  // 构建临时对象用于格式化
                  const tempHp = { hpid: hpadd.hpid, hl: hl };

                  // 根据盘口类型处理
                  switch (hpadd.hpid) {
                    case "4": // 全场让球
                      const ftHandicap = formatHandicap(tempHp);
                      if (ftHandicap !== '无盘口') {
                        // 添加优先级信息
                        const priority = hl.hn || 999;
                        odds.fullTimeHandicapList.push({ odds: ftHandicap, priority: priority });
                      }
                      break;
                    case "2": // 全场大小
                      const ftOverUnder = formatOverUnder(tempHp);
                      if (ftOverUnder !== '无盘口') {
                        // 添加优先级信息
                        const priority = hl.hn || 999;
                        odds.fullTimeOverUnderList.push({ odds: ftOverUnder, priority: priority });
                      }
                      break;
                    case "19": // 半场让球
                      const htHandicap = formatHandicap(tempHp);
                      if (htHandicap !== '无盘口') {
                        // 添加优先级信息
                        const priority = hl.hn || 999;
                        odds.halfTimeHandicapList.push({ odds: htHandicap, priority: priority });
                      }
                      break;
                    case "18": // 半场大小
                      const htOverUnder = formatOverUnder(tempHp);
                      if (htOverUnder !== '无盘口') {
                        // 添加优先级信息
                        const priority = hl.hn || 999;
                        odds.halfTimeOverUnderList.push({ odds: htOverUnder, priority: priority });
                      }
                      break;
                  }
                }
              }
            }
          }


          // 处理直接提供的赔率字段
          if (odds.fullTimeHandicapList.length === 0 && match.ah !== undefined) {
            // 如果直接提供了让球和赔率数据
            let ahValue = match.ah;
            // 确保保留原始符号
            if (typeof ahValue === 'number') {
              ahValue = ahValue > 0 ? `+${ahValue}` : `${ahValue}`;
            } else if (typeof ahValue === 'string') {
              // 如果是字符串但没有符号，尝试添加符号
              if (!ahValue.startsWith('+') && !ahValue.startsWith('-') && parseFloat(ahValue) > 0) {
                ahValue = `+${ahValue}`;
              }
            }

            const homeOdds = parseFloat(match.ahHome);
            const awayOdds = parseFloat(match.ahAway);

            // 只有当赔率值有效时才添加
            if (!isNaN(homeOdds) && !isNaN(awayOdds) && (homeOdds > 0 || awayOdds > 0)) {
              // 尝试确定让球方向
              const handicapDirection = match.ahd || (homeOdds < awayOdds ? 'h' : 'a');
              const handicapOdds = formatSimpleHandicap(ahValue, homeOdds, awayOdds, handicapDirection);
              if (handicapOdds !== '无盘口') {
                odds.fullTimeHandicapList.push({ odds: handicapOdds, priority: 999 });
              }
            }
          }

          if (odds.fullTimeOverUnderList.length === 0 && match.ou !== undefined) {
            // 如果直接提供了大小球和赔率数据
            const ouValue = parseFloat(match.ou);
            const overOdds = parseFloat(match.ouOver);
            const underOdds = parseFloat(match.ouUnder);

            // 只有当赔率值有效时才添加
            if (!isNaN(ouValue) && !isNaN(overOdds) && !isNaN(underOdds) && (overOdds > 0 || underOdds > 0)) {
              // 直接使用赔率，不做转换
              const formattedOverOdds = overOdds.toFixed(2);
              const formattedUnderOdds = underOdds.toFixed(2);
              const ouOdds = `大${ouValue}[${formattedOverOdds}];小${ouValue}[${formattedUnderOdds}]`;
              if (ouOdds !== '无盘口') {
                odds.fullTimeOverUnderList.push({ odds: ouOdds, priority: 999 });
              }
            }
          }

          // 处理hpsPns提供的盘口数据
          if (match.hpsPns && Array.isArray(match.hpsPns)) {
            // 提取全场让球盘
            const handicapPns = match.hpsPns.filter(pn => pn.hpid === "4");
            for (const handicapPn of handicapPns) {
              if (handicapPn && handicapPn.mct && handicapPn.ol && Array.isArray(handicapPn.ol) && handicapPn.ol.length >= 2) {
                const home = handicapPn.ol.find(o => o.ot === "1" || o.on === "主");
                const away = handicapPn.ol.find(o => o.ot === "2" || o.on === "客");

                if (home && away && home.ov && away.ov) {
                  const homeOdds = parseFloat(home.ov);
                  const awayOdds = parseFloat(away.ov);

                  if (!isNaN(homeOdds) && !isNaN(awayOdds)) {
                    // 保留原始盘口值的符号
                    let handicapValue = handicapPn.mct;
                    // 确保正确的符号
                    if (typeof handicapValue === 'number') {
                      handicapValue = handicapValue > 0 ? `+${handicapValue}` : `${handicapValue}`;
                    } else if (typeof handicapValue === 'string') {
                      // 如果是字符串但没有符号，尝试添加符号
                      if (!handicapValue.startsWith('+') && !handicapValue.startsWith('-') && parseFloat(handicapValue) > 0) {
                        handicapValue = `+${handicapValue}`;
                      }
                    }

                    // 尝试确定让球方向
                    const handicapDirection = handicapPn.hd || (homeOdds < awayOdds ? 'h' : 'a');
                    const handicapOdds = formatSimpleHandicap(handicapValue, homeOdds, awayOdds, handicapDirection);
                    if (handicapOdds !== '无盘口') {
                      odds.fullTimeHandicapList.push({ odds: handicapOdds, priority: 999 });
                    }
                  }
                }
              }
            }


            // 提取全场大小盘
            const ouPns = match.hpsPns.filter(pn => pn.hpid === "2");
            for (const ouPn of ouPns) {
              if (ouPn && ouPn.mct && ouPn.ol && Array.isArray(ouPn.ol) && ouPn.ol.length >= 2) {
                const over = ouPn.ol.find(o => o.ot === "Over" || o.on === "大");
                const under = ouPn.ol.find(o => o.ot === "Under" || o.on === "小");

                if (over && under && over.ov && under.ov) {
                  const overOdds = parseFloat(over.ov);
                  const underOdds = parseFloat(under.ov);
                  const ouValue = parseFloat(ouPn.mct);

                  if (!isNaN(overOdds) && !isNaN(underOdds) && !isNaN(ouValue)) {
                    // 直接使用赔率，不做转换
                    const formattedOverOdds = overOdds.toFixed(2);
                    const formattedUnderOdds = underOdds.toFixed(2);
                    const ouOdds = `大${ouValue}[${formattedOverOdds}];小${ouValue}[${formattedUnderOdds}]`;
                    if (ouOdds !== '无盘口') {
                      odds.fullTimeOverUnderList.push({ odds: ouOdds, priority: 999 });
                    }
                  }
                }
              }
            }

            // 提取半场让球盘
            const halfHandicapPns = match.hpsPns.filter(pn => pn.hpid === "19");
            for (const halfHandicapPn of halfHandicapPns) {
              if (halfHandicapPn && halfHandicapPn.mct && halfHandicapPn.ol && Array.isArray(halfHandicapPn.ol) && halfHandicapPn.ol.length >= 2) {
                const home = halfHandicapPn.ol.find(o => o.ot === "1" || o.on === "主");
                const away = halfHandicapPn.ol.find(o => o.ot === "2" || o.on === "客");

                if (home && away && home.ov && away.ov) {
                  const homeOdds = parseFloat(home.ov);
                  const awayOdds = parseFloat(away.ov);

                  if (!isNaN(homeOdds) && !isNaN(awayOdds)) {
                    // 保留原始盘口值的符号
                    let handicapValue = halfHandicapPn.mct;
                    // 确保正确的符号
                    if (typeof handicapValue === 'number') {
                      handicapValue = handicapValue > 0 ? `+${handicapValue}` : `${handicapValue}`;
                    } else if (typeof handicapValue === 'string') {
                      // 如果是字符串但没有符号，尝试添加符号
                      if (!handicapValue.startsWith('+') && !handicapValue.startsWith('-') && parseFloat(handicapValue) > 0) {
                        handicapValue = `+${handicapValue}`;
                      }
                    }

                    // 尝试确定让球方向
                    const handicapDirection = halfHandicapPn.hd || (homeOdds < awayOdds ? 'h' : 'a');
                    const handicapOdds = formatSimpleHandicap(handicapValue, homeOdds, awayOdds, handicapDirection);
                    if (handicapOdds !== '无盘口') {
                      odds.halfTimeHandicapList.push({ odds: handicapOdds, priority: 999 });
                    }
                  }
                }
              }
            }

            // 提取半场大小盘
            const halfOuPns = match.hpsPns.filter(pn => pn.hpid === "18");
            for (const halfOuPn of halfOuPns) {
              if (halfOuPn && halfOuPn.mct && halfOuPn.ol && Array.isArray(halfOuPn.ol) && halfOuPn.ol.length >= 2) {
                const over = halfOuPn.ol.find(o => o.ot === "Over" || o.on === "大");
                const under = halfOuPn.ol.find(o => o.ot === "Under" || o.on === "小");

                if (over && under && over.ov && under.ov) {
                  const overOdds = parseFloat(over.ov);
                  const underOdds = parseFloat(under.ov);
                  const ouValue = parseFloat(halfOuPn.mct);

                  if (!isNaN(overOdds) && !isNaN(underOdds) && !isNaN(ouValue)) {
                    // 直接使用赔率，不做转换
                    const formattedOverOdds = overOdds.toFixed(2);
                    const formattedUnderOdds = underOdds.toFixed(2);
                    const ouOdds = `大${ouValue}[${formattedOverOdds}];小${ouValue}[${formattedUnderOdds}]`;
                    if (ouOdds !== '无盘口') {
                      odds.halfTimeOverUnderList.push({ odds: ouOdds, priority: 999 });
                    }
                  }
                }
              }
            }
          }


          // 按照hn优先级排序盘口
          sortByPriority(odds.fullTimeHandicapList);
          sortByPriority(odds.fullTimeOverUnderList);
          sortByPriority(odds.halfTimeHandicapList);
          sortByPriority(odds.halfTimeOverUnderList);

          // 转换盘口数组为新的格式
          const convertHandicapList = (oddsList) => {
            return oddsList.map(item => {
              // 解析让球盘口字符串，例如: "-0.5[1.95];+0.5[1.85]"
              const parts = item.odds.split(';');
              if (parts.length >= 2) {
                const homePart = parts[0].match(/(.+)\[(.+)\]/);
                const awayPart = parts[1].match(/(.+)\[(.+)\]/);
                if (homePart && awayPart) {
                  return {
                    homeHandicap: homePart[1],
                    homeOdds: homePart[2],
                    awayHandicap: awayPart[1],
                    awayOdds: awayPart[2]
                  };
                }
              }
              return null;
            }).filter(item => item !== null);
          };

          const convertOverUnderList = (oddsList) => {
            return oddsList.map(item => {
              // 解析大小球盘口字符串，例如: "大2.5[1.90];小2.5[1.90]"
              const parts = item.odds.split(';');
              if (parts.length >= 2) {
                const overPart = parts[0].match(/大(.+)\[(.+)\]/);
                const underPart = parts[1].match(/小(.+)\[(.+)\]/);
                if (overPart && underPart) {
          return {
                    overTotal: `大 ${overPart[1]}`,
                    overOdds: overPart[2],
                    underTotal: `小 ${underPart[1]}`,
                    underOdds: underPart[2]
                  };
                }
              }
              return null;
            }).filter(item => item !== null);
          };

          // 返回新格式的数组结构
          return {
            fullTimeHandicapList: convertHandicapList(odds.fullTimeHandicapList),
            fullTimeOverUnderList: convertOverUnderList(odds.fullTimeOverUnderList),
            halfTimeHandicapList: convertHandicapList(odds.halfTimeHandicapList),
            halfTimeOverUnderList: convertOverUnderList(odds.halfTimeOverUnderList)
          };
        } catch (error) {
          return {
            fullTimeHandicapList: [],
            fullTimeOverUnderList: [],
            halfTimeHandicapList: [],
            halfTimeOverUnderList: []
          };
        }
      } catch (error) {
        return {
          fullTimeHandicapList: [],
          fullTimeOverUnderList: [],
          halfTimeHandicapList: [],
          halfTimeOverUnderList: []
        };
      }
    }

    /**
     * 根据优先级排序盘口列表
     * @param {Array} oddsList - 盘口列表，每个元素包含odds和priority字段
     */
    function sortByPriority(oddsList) {
      if (!oddsList || oddsList.length <= 1) return;

      // 按照priority字段从小到大排序（小的优先级值排在前面）
      oddsList.sort((a, b) => {
        // 如果优先级相同，保持原有顺序
        if (a.priority === b.priority) return 0;
        // 优先级值小的排在前面
        return a.priority - b.priority;
      });
    }

    /**
     * 格式化让球盘口
     * @param {Object} hp - 让球盘口数据
     * @returns {string} - 格式化后的让球盘口字符串
     */
    function formatHandicap(hp) {
      try {
        if (!hp.hl) return '';

        // 获取让球值，转换为小数形式
        let handicap = hp.hl.hv !== undefined ? hp.hl.hv : "0";

        // 将分数形式转换为小数
        if (handicap.toString().includes('/')) {
          const parts = handicap.toString().split('/');
          if (parts.length === 2) {
            const num1 = parseFloat(parts[0]);
            const num2 = parseFloat(parts[1]);
            if (!isNaN(num1) && !isNaN(num2)) {
              // 特殊处理常见的分数形式让球值
              if ((num1 === 0 && num2 === 0.5) || (num1 === -0 && num2 === 0.5)) {
                // 0/0.5 或 -0/0.5 转换为 0.25 或 -0.25
                handicap = num1 < 0 ? "-0.25" : "0.25";
              } else if ((num1 === 0.5 && num2 === 1) || (num1 === -0.5 && num2 === 1)) {
                // 0.5/1 或 -0.5/1 转换为 0.75 或 -0.75
                handicap = num1 < 0 ? "-0.75" : "0.75";
              } else {
                // 其他分数形式使用平均值
              handicap = ((num1 + num2) / 2).toFixed(2);
              }
            }
          }
        }

        // 记录让球方向（如果API提供）
        const handicapDirection = hp.hl.hd; // 如果存在，可能是"h"(主队)或"a"(客队)

        // 检查赔率列表
        if (!hp.hl.ol || !Array.isArray(hp.hl.ol) || hp.hl.ol.length < 2) {
          return '';
        }

        // 找到主队和客队选项
        const home = hp.hl.ol.find(o => o.ot === "1");
        const away = hp.hl.ol.find(o => o.ot === "2");

        if (!home || !away) {
          return '';
        }

        // 检查onb字段中的正负号判定
        if (home.onb && typeof home.onb === 'string') {
          if (home.onb.startsWith('+')) {
            handicap = '+' + handicap.toString().replace(/^[+-]/, '');
          } else if (home.onb.startsWith('-')) {
            handicap = '-' + handicap.toString().replace(/^[+-]/, '');
          }
        }

        // 获取赔率值
        let homeOdds = null;
        let awayOdds = null;

        if (home.ov) {
          homeOdds = parseFloat(home.ov) > 100 ? parseFloat(home.ov) / 100000 : parseFloat(home.ov);
        } else if (home.ov2) {
          homeOdds = parseFloat(home.ov2);
        }

        if (away.ov) {
          awayOdds = parseFloat(away.ov) > 100 ? parseFloat(away.ov) / 100000 : parseFloat(away.ov);
        } else if (away.ov2) {
          awayOdds = parseFloat(away.ov2);
        }

        // 检查是否有有效的赔率值
        if (homeOdds === null || awayOdds === null || isNaN(homeOdds) || isNaN(awayOdds)) {
          return '';
        }

        // 将handicapDirection信息传递给formatSimpleHandicap，以确保正确的让球方向
        return formatSimpleHandicap(handicap, homeOdds, awayOdds, handicapDirection);
      } catch (error) {
        return '';
      }
    }

    /**
     * 格式化大小球盘口
     * @param {Object} hp - 大小球盘口数据
     * @returns {string} - 格式化后的大小球盘口字符串
     */
    function formatOverUnder(hp) {
      try {
        if (!hp.hl) return '';

        // 获取盘口值
        let ouValue = hp.hl.hv || "2.5";

        // 将分数形式转换为小数
        if (ouValue.toString().includes('/')) {
          const parts = ouValue.toString().split('/');
          if (parts.length === 2) {
            const num1 = parseFloat(parts[0]);
            const num2 = parseFloat(parts[1]);
            if (!isNaN(num1) && !isNaN(num2)) {
              // 特殊处理常见的分数形式大小球值
              if (num1 === 0 && num2 === 0.5) {
                // 0/0.5 转换为 0.25
                ouValue = "0.25";
              } else if (num1 === 0.5 && num2 === 1) {
                // 0.5/1 转换为 0.75
                ouValue = "0.75";
              } else {
                // 其他分数形式使用平均值
              ouValue = ((num1 + num2) / 2).toFixed(2);
              }
            }
          }
        }

        // 检查赔率列表
        if (!hp.hl.ol || !Array.isArray(hp.hl.ol) || hp.hl.ol.length < 2) {
          return '';
        }

        // 找到大球和小球选项
        const over = hp.hl.ol.find(o => o.ot === "Over" || (o.on && o.on.includes('大')));
        const under = hp.hl.ol.find(o => o.ot === "Under" || (o.on && o.on.includes('小')));

        if (!over || !under) {
          return '';
        }

        // 获取赔率值
        let overOdds = null;
        let underOdds = null;

        if (over.ov) {
          overOdds = parseFloat(over.ov) > 100 ? parseFloat(over.ov) / 100000 : parseFloat(over.ov);
        } else if (over.ov2) {
          overOdds = parseFloat(over.ov2);
        }

        if (under.ov) {
          underOdds = parseFloat(under.ov) > 100 ? parseFloat(under.ov) / 100000 : parseFloat(under.ov);
        } else if (under.ov2) {
          underOdds = parseFloat(under.ov2);
        }

        // 检查是否有有效的赔率值
        if (overOdds === null || underOdds === null || isNaN(overOdds) || isNaN(underOdds)) {
          return '';
        }

        // 将欧洲盘转换为香港盘 (减1)
        overOdds = overOdds - 1;
        underOdds = underOdds - 1;

        // 格式化赔率为两位小数
        const formattedOverOdds = overOdds.toFixed(2);
        const formattedUnderOdds = underOdds.toFixed(2);

        return `大${ouValue}[${formattedOverOdds}];小${ouValue}[${formattedUnderOdds}]`;
      } catch (error) {
        return '';
      }
    }

    /**
     * 格式化简单让球盘
     * @param {any} handicap - 让球值
     * @param {any} homeOdds - 主队赔率
     * @param {any} awayOdds - 客队赔率
     * @param {string} [handicapDirection] - 让球方向，'h'表示主队让球，'a'表示客队让球
     * @returns {string} - 格式化后的让球盘口字符串
     */
    function formatSimpleHandicap(handicap, homeOdds, awayOdds, handicapDirection) {
      try {
        // 确保数据类型
        let handicapValue = typeof handicap === 'string' ? handicap : String(handicap);
        let homeOddsValue = parseFloat(homeOdds);
        let awayOddsValue = parseFloat(awayOdds);

        // 如果没有提供有效的赔率，则返回无盘口
        if (isNaN(homeOddsValue) || isNaN(awayOddsValue)) {
          return '';
        }

        // 将欧洲盘转换为香港盘 (减1)
        homeOddsValue = homeOddsValue - 1;
        awayOddsValue = awayOddsValue - 1;

        // 格式化赔率为两位小数
        const formattedHomeOdds = homeOddsValue.toFixed(2);
        const formattedAwayOdds = awayOddsValue.toFixed(2);

        let homeHandicap, awayHandicap;

        // 查看onb字段中的正负号判定直接让球方向
        // 处理已经包含正负号的情况 - 保留原始符号
        if (handicapValue.startsWith('+') || handicapValue.startsWith('-')) {
          // 完全保留原始符号
          homeHandicap = handicapValue;
          // 为对方设置相反的符号
          if (handicapValue.startsWith('+')) {
            awayHandicap = '-' + handicapValue.substring(1);
          } else if (handicapValue.startsWith('-')) {
            awayHandicap = '+' + handicapValue.substring(1);
          }
        }
        // 处理特殊让球值(如 "0/0.5" 或 "0.5/1")
        else if (handicapValue.includes('/')) {
          // 将分数形式转换为小数
          const parts = handicapValue.split('/');
          if (parts.length === 2) {
            const num1 = parseFloat(parts[0]);
            const num2 = parseFloat(parts[1]);
            if (!isNaN(num1) && !isNaN(num2)) {
              handicapValue = ((num1 + num2) / 2).toFixed(2);
            }
          }

          // 尝试使用handicapDirection确定主客队让球方向
          if (handicapDirection === 'h') {
            // 主队让球
            homeHandicap = `-${handicapValue}`;
            awayHandicap = `+${handicapValue}`;
          } else if (handicapDirection === 'a') {
            // 客队让球
            homeHandicap = `+${handicapValue}`;
            awayHandicap = `-${handicapValue}`;
          } else {
            // 如果没有明确的让球方向，默认主队让球
              homeHandicap = `-${handicapValue}`;
              awayHandicap = `+${handicapValue}`;
          }
        }
        // 处理平手盘(0)或普通数字
        else {
          const numHandicap = parseFloat(handicapValue);

          if (isNaN(numHandicap) || numHandicap === 0) {
            // 平手盘
            homeHandicap = '0';
            awayHandicap = '0';
          } else {
            // 普通数字，使用handicapDirection确定让球方向
            if (handicapDirection === 'h') {
              // 主队让球
              homeHandicap = `-${Math.abs(numHandicap)}`;
              awayHandicap = `+${Math.abs(numHandicap)}`;
            } else if (handicapDirection === 'a') {
              // 客队让球
              homeHandicap = `+${Math.abs(numHandicap)}`;
              awayHandicap = `-${Math.abs(numHandicap)}`;
            } else {
              // 如果没有明确的让球方向，默认主队让球
                homeHandicap = `-${Math.abs(numHandicap)}`;
                awayHandicap = `+${Math.abs(numHandicap)}`;
            }
          }
        }

        // 返回最终格式化的让球盘口：主队盘口[主队赔率];客队盘口[客队赔率]
        return `${homeHandicap}[${formattedHomeOdds}];${awayHandicap}[${formattedAwayOdds}]`;
      } catch (error) {
        return '';
      }
    }

    /**
     * 将马来盘赔率转换为香港盘赔率
     * @param {string} malaysianOdds - 马来盘赔率（例如"-0.89"或"0.79"）
     * @returns {string} - 香港盘赔率
     */
    function convertMalaysianToHongKong(malaysianOdds) {
      // 不再转换，直接返回原始赔率
      try {
        // 确保输入是字符串
        const oddsStr = String(malaysianOdds);

        // 解析赔率值
        const odds = parseFloat(oddsStr);

        // 检查是否为有效数字
        if (isNaN(odds)) {
          return "0.00";
        }

        // 不做转换，直接返回格式化后的原始赔率
        return odds.toFixed(2);
      } catch (error) {
        return "0.00";
      }
    }

    function formatOddsForDisplay(oddsStr) {
      if (!oddsStr) {
        return '';
      }

      // 分割多个盘口
      const odds = oddsStr.split('|');

      // 处理每个盘口，确保紧凑显示格式
      const formattedOdds = odds.map(odd => {
        if (odd.includes('[') && odd.includes(';')) {
          const parts = odd.split(';');
          if (parts.length === 2) {
            const homeOdds = parts[0].trim();
            const awayOdds = parts[1].trim();

            // 紧凑显示盘口
            return `${homeOdds};${awayOdds}`;
          }
        }

        // 默认返回原始格式
        return odd;
      });

      // 用<br>标签连接不同的盘口，实现换行显示
      return formattedOdds.join('<br>');
    }

    // 添加自动刷新相关变量和函数
    let refreshIntervalId = null;
    let countdownIntervalId = null;
    let isAutoRefreshActive = false;

    // 切换自动刷新状态
    function toggleAutoRefresh() {
      console.log('切换自动刷新状态:', isAutoRefreshActive);
      if (isAutoRefreshActive) {
        stopAutoRefresh();
      } else {
        startAutoRefresh();
      }
    }

    // 添加Page Visibility API支持
    let isPageVisible = true;
    let hiddenProperty = 'hidden';
    
    // 检测浏览器支持的visibility属性名称
    if ('hidden' in document) {
      hiddenProperty = 'hidden';
    } else if ('webkitHidden' in document) {
      hiddenProperty = 'webkitHidden';
    } else if ('mozHidden' in document) {
      hiddenProperty = 'mozHidden';
    } else if ('msHidden' in document) {
      hiddenProperty = 'msHidden';
    }
    
    // 监听可见性变化事件
    const visibilityChangeEvent = hiddenProperty.replace(/hidden/i, 'visibilitychange');
    
    // 页面可见性变化时的处理函数
    document.addEventListener(visibilityChangeEvent, function() {
      isPageVisible = !document[hiddenProperty];
      console.log('页面可见性改变:', isPageVisible ? '可见' : '不可见');
      
      // 如果页面变为不可见状态，不调整刷新频率，保持原有设置
      // 只记录状态变化
      if (!isPageVisible) {
        console.log('页面进入后台，继续使用原有刷新频率');
      } 
      // 如果页面变为可见状态，记录状态
      else if (isPageVisible) {
        console.log('页面恢复前台显示');
      }
    }, false);
    
    // 优化后的keepAlive函数，减少能耗
    function keepAlive() {
      try {
        // 检查document是否仍然可用
        if (typeof document === 'undefined' || !document) return;
        
        if (!document[hiddenProperty]) {
          // 页面可见时使用requestAnimationFrame
          requestAnimationFrame(keepAlive);
          return;
      }
      
        // 页面不可见时，降低刷新频率到5秒一次，减少能耗
        setTimeout(function() {
          try {
            if (typeof document !== 'undefined' && document) {
              keepAlive();
            }
          } catch (e) {
            // 静默处理错误
      }
        }, 5000); // 降低到5秒执行一次
      } catch (e) {
        // 静默处理错误
      }
    }
    
    // 安全启动keepAlive
    try {
      keepAlive();
    } catch (e) {
      console.log('启动keepAlive时出错:', e);
    }
    
    // 保存最近一次的原始响应数据
    let lastRawResponse = null;
    
    // 重置为默认值函数
    function resetToDefault(inputId) {
      const element = document.getElementById(inputId);
      const statusElement = document.getElementById(inputId + 'Status');
      
      if (element) {
        if (inputId === 'apiUrl') {
          element.value = 'https://api.dl9qfnzr.com/yewu11/v1/w/structureMatchesPB?t=1747483352030';
        } else if (inputId === 'requestBody') {
          element.value = '{"cuid":"523963395904882753","sort":1,"tid":"","apiType":1,"euid":"30002","cpn":1,"cps":500}';
        } else if (inputId === 'requestId') {
          element.value = 'f9a9faaeecd10806e5692dde93ef92996711e5e1';
        }
        
        // 显示状态
        if (statusElement) {
          statusElement.style.display = 'inline';
          statusElement.textContent = '已恢复默认';
          
          // 2秒后隐藏状态
          setTimeout(() => {
            statusElement.style.display = 'none';
          }, 2000);
        }
      }
    }
    
    function startAutoRefresh() {
      if (refreshIntervalId) {
        clearInterval(refreshIntervalId);
      }
      
      if (countdownIntervalId) {
        clearInterval(countdownIntervalId);
      }
      
      const refreshIntervalEl = document.getElementById('refresh-interval');
      const interval = refreshIntervalEl ? parseInt(refreshIntervalEl.value) : 2500;
      if (isNaN(interval) || interval < 1000) {
        alert('请输入有效的刷新间隔，最小为1000毫秒');
        if (refreshIntervalEl) refreshIntervalEl.value = 2500;
        return;
      }
      
      isAutoRefreshActive = true;
      const toggleRefreshEl = document.getElementById('toggle-refresh');
      const refreshStatusEl = document.getElementById('refresh-status');
      
      if (toggleRefreshEl) {
        toggleRefreshEl.textContent = '停止自动刷新';
        toggleRefreshEl.classList.add('active');
      }
      
      if (refreshStatusEl) {
        refreshStatusEl.textContent = '下次刷新: ' + interval / 1000 + '秒后';
        refreshStatusEl.classList.add('active');
      }
      
      let countdown = interval / 1000;
      countdownIntervalId = setInterval(() => {
        countdown -= 1;
        if (countdown < 0) countdown = interval / 1000;
        const refreshStatusEl = document.getElementById('refresh-status');
        if (refreshStatusEl) {
          refreshStatusEl.textContent = '下次刷新: ' + countdown + '秒后';
        }
      }, 1000);
      
      // 根据页面可见性调整刷新间隔，降低能耗
      refreshIntervalId = setInterval(() => {
        // 页面不可见时，降低刷新频率
        if (!isPageVisible) {
          return; // 页面不可见时不刷新，节省资源
        }
        
        const postMethodEl = document.getElementById('postMethod');
        const method = postMethodEl && postMethodEl.checked ? 'POST' : 'GET';
        
        // 每次读取输入框的最新内容
        const requestBodyEl = document.getElementById('requestBody');
        const apiUrlEl = document.getElementById('apiUrl');
        const requestBody = requestBodyEl ? requestBodyEl.value.trim() : '';
        const apiUrl = apiUrlEl ? apiUrlEl.value.trim() : '';
        
        const refreshStatusEl = document.getElementById('refresh-status');
        if (refreshStatusEl) {
          refreshStatusEl.innerHTML = '<span class="loading-spinner"></span>正在刷新...';
        }
        
        sendRequest(method, apiUrl, requestBody);
        countdown = interval / 1000;
      }, interval);
      
      // 删除控制台输出，减少能耗
    }
    
    function stopAutoRefresh() {
      if (refreshIntervalId) {
        clearInterval(refreshIntervalId);
        refreshIntervalId = null;
      }
      
      if (countdownIntervalId) {
        clearInterval(countdownIntervalId);
        countdownIntervalId = null;
      }
      
      isAutoRefreshActive = false;
      
      const toggleRefreshEl = document.getElementById('toggle-refresh');
      const refreshStatusEl = document.getElementById('refresh-status');
      
      if (toggleRefreshEl) {
        toggleRefreshEl.textContent = '开启自动刷新';
        toggleRefreshEl.classList.remove('active');
      }
      
      if (refreshStatusEl) {
        refreshStatusEl.textContent = '';
        refreshStatusEl.classList.remove('active');
      }
      
      // 删除控制台输出，减少能耗
    }
    
    document.addEventListener('DOMContentLoaded', function() {
      // 检查pako库是否正确加载
      if (typeof pako === 'undefined') {
        console.warn('pako库未正确加载，GZIP解压功能可能不可用');
        updateStatus('警告: pako库未加载，GZIP解压功能不可用', true);
        
        // 尝试重新加载pako库
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/pako@2.1.0/dist/pako.min.js'; // 使用备用CDN
        script.onload = function() {
          console.log('pako库重新加载成功');
          updateStatus('pako库已重新加载');
        };
        script.onerror = function() {
          console.error('pako库重新加载失败，尝试另一个CDN');
          // 尝试另一个CDN
          const script2 = document.createElement('script');
          script2.src = 'https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js';
          script2.onload = function() {
            console.log('pako库从备用CDN加载成功');
            updateStatus('pako库已从备用CDN加载');
          };
          script2.onerror = function() {
            console.error('所有CDN的pako库都加载失败');
            updateStatus('错误: 无法加载pako库，GZIP解压功能不可用', true);
          };
          document.head.appendChild(script2);
        };
        document.head.appendChild(script);
      } else {
        console.log('pako库已正确加载');
      }
      
      // 获取DOM元素
      const sendRequestButton = document.getElementById('sendRequest');
      const postMethodRadio = document.getElementById('postMethod');
      const requestBodyInput = document.getElementById('requestBody');
      const apiUrlInput = document.getElementById('apiUrl');
      const toggleRefreshButton = document.getElementById('toggle-refresh');
      const showRawDataButton = document.getElementById('showRawData');

      // 显示原始数据按钮事件
      if (showRawDataButton) {
      showRawDataButton.addEventListener('click', function() {
        if (lastRawResponse) {
          // 删除控制台输出，减少能耗
          updateStatus('已处理最近一次请求的原始数据');
        } else {
          updateStatus('暂无原始数据，请先发送请求', true);
        }
      });
      }
      
      // 自动刷新按钮事件
      // 已通过HTML的onclick属性绑定，不需要再通过JavaScript绑定
      /*
      if (toggleRefreshButton) {
      toggleRefreshButton.addEventListener('click', function() {
          console.log('自动刷新按钮被点击');
          toggleAutoRefresh();
        });
      }
      */
      
      // 发送请求按钮事件
      if (sendRequestButton) {
      sendRequestButton.addEventListener('click', function() {
        // 确保获取输入框的最新内容
          const method = postMethodRadio && postMethodRadio.checked ? 'POST' : 'GET';
          const requestBody = requestBodyInput ? requestBodyInput.value.trim() : '';
          const apiUrl = apiUrlInput ? apiUrlInput.value.trim() : '';
        
        // 发送请求
        sendRequest(method, apiUrl, requestBody);
      });
      }
      
      // 设置API数据输入框的事件监听器
      const apiDataInput = document.getElementById('apiDataInput');
      if (apiDataInput) {
        // 添加输入事件监听器，实现自动转换
        apiDataInput.addEventListener('input', autoConvertApiDataToJson);
        apiDataInput.addEventListener('paste', function() {
          // 延迟执行，确保粘贴的内容已经插入
          setTimeout(autoConvertApiDataToJson, 10);
        });
        apiDataInput.addEventListener('blur', autoConvertApiDataToJson);
      }
      
      // 自动发送请求
      setTimeout(() => {
        // 删除控制台输出，减少能耗
        const method = postMethodRadio && postMethodRadio.checked ? 'POST' : 'GET';
        const requestBody = requestBodyInput ? requestBodyInput.value.trim() : '';
        const apiUrl = apiUrlInput ? apiUrlInput.value.trim() : '';
        sendRequest(method, apiUrl, requestBody);
      }, 500);
    });

    function updateStatus(message, isError = false) {
      const statusEl = document.getElementById('status');
      if (!statusEl) return;
      
      statusEl.style.display = 'block';
      statusEl.textContent = message;
      statusEl.className = isError ? 'status error' : 'status success';
    }
    
    // 解码Base64和GZIP压缩的数据
    function decodeGzippedBase64(base64Data) {
      try {
        // 检查pako库是否可用
        if (typeof pako === 'undefined') {
          console.error('pako库未加载，无法解压GZIP数据');
          throw new Error('pako库未加载');
        }
        
        // 将Base64转为二进制数据
        const binaryString = atob(base64Data);
        
        const len = binaryString.length;
        const bytes = new Uint8Array(len);
        
        for (let i = 0; i < len; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        
        // 使用pako解压GZIP数据
        const decompressed = pako.inflate(bytes);
        
        // 将解压后的数据转为UTF-8字符串
        const decoder = new TextDecoder('utf-8');
        const jsonString = decoder.decode(decompressed);
        
        // 尝试解析JSON
        const parsed = JSON.parse(jsonString);
        
        // 删除控制台输出，减少能耗
        
        return parsed;
      } catch (error) {
        console.error('解码GZIP和Base64数据失败:', error);
        throw error;
      }
    }

    function sendRequest(method, apiUrl, requestBody) {
      if (!apiUrl || apiUrl.trim() === '') {
        updateStatus('请输入有效的API URL', true);
        return;
      }
      
      updateStatus(`正在发送 ${method} 请求...`);
      console.log(`使用XMLHttpRequest发送 ${method} 请求...`);
      
      // 获取当前时间戳
      const timestamp = Date.now();
      
      // 获取用户输入的requestId
      const requestIdEl = document.getElementById('requestId');
      const requestId = requestIdEl && requestIdEl.value ? requestIdEl.value.trim() : '5ccf745594240ce629e89b41d7bee9665bdb12a1';
      
      // 构建checkId
      const checkId = `pc-${requestId}-523963395904882753-${timestamp}`;
      
      // 创建XHR对象
      const xhr = new XMLHttpRequest();
      
      // 添加时间戳参数到URL
      const urlWithTimestamp = apiUrl.includes('?') 
        ? `${apiUrl}&t=${timestamp}` 
        : `${apiUrl}?t=${timestamp}`;
      
      // 配置请求
      xhr.open(method, urlWithTimestamp, true);
      
      // 设置请求头 - 只保留安全的请求头
      xhr.setRequestHeader('accept', 'application/json, text/plain, */*');
      xhr.setRequestHeader('accept-language', 'zh-CN,zh;q=0.9');
      xhr.setRequestHeader('checkId', checkId);
      xhr.setRequestHeader('lang', 'zh');
      xhr.setRequestHeader('request-code', '{"panda-bss-source":"2"}');
      xhr.setRequestHeader('requestId', requestId);
      
      // 如果是POST请求，设置Content-Type
      if (method === 'POST') {
        xhr.setRequestHeader('Content-Type', 'application/json');
      }
      
      // 设置超时时间
      xhr.timeout = 10000; // 10秒
      
      // 处理响应
      xhr.onload = function() {
        // 更新刷新状态（如果自动刷新已启用）
        if (isAutoRefreshActive) {
          const refreshStatusEl = document.getElementById('refresh-status');
          if (refreshStatusEl) {
            const refreshIntervalEl = document.getElementById('refresh-interval');
            const interval = refreshIntervalEl ? parseInt(refreshIntervalEl.value) : 2500;
            refreshStatusEl.textContent = '下次刷新: ' + interval / 1000 + '秒后';
          }
        }
        
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const responseData = JSON.parse(xhr.responseText);
            
            // 保存最近一次的原始响应数据
            lastRawResponse = responseData;
            
            // 将解压后的数据内容填充到API数据输入框
            const apiDataInput = document.getElementById('apiDataInput');
            if (apiDataInput) {
              let dataToDisplay = responseData;
              
              // 如果有data字段且是字符串，尝试解压
              if (responseData.data && typeof responseData.data === 'string') {
                try {
                  // 尝试GZIP解码
                  const decodedData = decodeGzippedBase64(responseData.data);
                  dataToDisplay = decodedData;
                  console.log('API数据输入框显示解压后的数据');
                } catch (decodeErr) {
                  console.warn('GZIP解码失败，尝试直接解析:', decodeErr.message);
                  // 如果GZIP解码失败，尝试直接解析
                  try {
                    if (responseData.data.startsWith('{') || responseData.data.startsWith('[')) {
                      dataToDisplay = JSON.parse(responseData.data);
                      console.log('API数据输入框显示直接解析的数据');
                    } else {
                      dataToDisplay = responseData; // 保持原始响应
                    }
                  } catch (directErr) {
                    dataToDisplay = responseData; // 保持原始响应
                  }
                }
              }
              
              apiDataInput.value = JSON.stringify(dataToDisplay, null, 2);
              // 触发自动转换
              autoConvertApiDataToJson();
            }
            
            updateStatus('请求成功!');
            
            // 尝试解码数据并输出到控制台
            try {
              // 特殊检查data字段
              if (responseData.data && typeof responseData.data === 'string') {
                try {
                  // 检查是否为成功响应但没有实际数据
                  if (responseData.code === '0000000' && responseData.msg === '成功') {
                    console.log('API返回成功状态，尝试解码data字段');
                  }
                  
                  const decodedData = decodeGzippedBase64(responseData.data);
                  // 此处不需要重复输出原始API数据，因为decodeGzippedBase64函数已经输出
                  console.log('解码数据成功，类型:', typeof decodedData);
                  console.log('顶层字段:', Object.keys(decodedData));
                  
                  // 使用ob.js转换解码后的数据为HG格式并输出到控制台
                  obMatchesData = convertObDataToHgFormat(decodedData);
                  console.log('OB.js转换内容:', obMatchesData);
                  
                  // 更新表格
                  updateOBTable();
                  
                  // 如果转换结果没有赛事
                  if (obMatchesData.matches.length === 0) {
                    console.warn('转换结果没有赛事，尝试其他方法解析');
                  }
                  
                  return;
                } catch (decodeErr) {
                  console.warn('GZIP解码失败，尝试直接处理data字段:', decodeErr.message);
                  // 尝试直接解析
                  try {
                    let directData = responseData.data;
                    
                    // 有时候API返回的是JSON字符串而不是Base64+GZIP
                    if (typeof directData === 'string' && (directData.startsWith('{') || directData.startsWith('['))) {
                      directData = JSON.parse(directData);
                      
                      // 删除控制台输出，减少能耗
                      
                  obMatchesData = convertObDataToHgFormat(directData);
                      // 更新表格
                      updateOBTable();
                      return;
                    } else {
                      // 如果data字段不是JSON字符串，尝试直接使用整个响应数据
                      console.log('尝试使用整个响应数据进行转换');
                      obMatchesData = convertObDataToHgFormat(responseData);
                      // 更新表格
                      updateOBTable();
                      return;
                    }
                  } catch (directErr) {
                    console.warn('直接解析也失败:', directErr.message);
                  }
                }
              } else if (responseData.data) {
                // 删除控制台输出，减少能耗
                
                obMatchesData = convertObDataToHgFormat(responseData.data);
                // 更新表格
                updateOBTable();
                return;
              }
              
              // 如果没有找到可解码的数据，尝试使用整个响应
              // 删除控制台输出，减少能耗
              
              obMatchesData = convertObDataToHgFormat(responseData);
              // 更新表格
              updateOBTable();
            } catch (decodeErr) {
            }
          } catch (error) {
            updateStatus('响应数据解析失败: ' + error.message, true);
          }
        } else {
          const errorMsg = `HTTP错误! 状态码: ${xhr.status}`;
          updateStatus(errorMsg, true);
        }
      };
      
      // 超时处理
      xhr.ontimeout = function() {
        updateStatus('请求超时', true);
      };
      
      // 错误处理
      xhr.onerror = function() {
        updateStatus('请求失败，可能是CORS跨域限制或网络问题', true);
        console.error('请求失败，请检查网络连接或API地址');
      };
      
      // 发送请求
      if (method === 'POST' && requestBody) {
        console.log('POST请求体:', requestBody);
        try {
          // 尝试解析JSON以确保有效，然后发送
          const jsonBody = JSON.parse(requestBody);
          xhr.send(JSON.stringify(jsonBody));
        } catch (e) {
          updateStatus('请求体JSON格式无效: ' + e.message, true);
        }
      } else {
        xhr.send();
      }
    }
  </script>
  
  <!-- 确保在页面加载完成后显式输出OB对象内容 -->
  <script>
    // WebSocket连接相关变量
    let socket = null;
    let wsConnected = false;
    let wsReconnectTimer = null;
    const WS_SERVER_URL = 'ws://**************:6080';
    const WS_RECONNECT_INTERVAL = 3000; // 重连间隔：3秒
    
    // 通用数据处理函数 - 不再使用加密
    /**
     * 数据加密函数
     * @param {any} data - 任何格式的数据
     * @returns {string} - 加密后的字符串
     */
    function encryptData(data) {
      try {
        // 将数据转换为字符串
        const dataString = typeof data === 'string' ? data : JSON.stringify(data);
        
        // 使用固定密码进行简单加密
        const password = "mddh.2d33gg";
        let encrypted = "";
        
        // 简单的XOR加密
        for (let i = 0; i < dataString.length; i++) {
          const charCode = dataString.charCodeAt(i) ^ password.charCodeAt(i % password.length);
          encrypted += String.fromCharCode(charCode);
        }
        
        // 转换为Base64以便传输 - 使用encodeURIComponent处理Unicode字符
        return btoa(encodeURIComponent(encrypted).replace(/%([0-9A-F]{2})/g, (match, p1) => {
          return String.fromCharCode('0x' + p1);
        }));
      } catch (error) {
        console.error('加密失败:', error);
        return null;
      }
    }
    
    // 初始化WebSocket连接
    function initWebSocket() {
      if (socket) {
        // 清理之前的连接
        socket.onclose = null;
        socket.onerror = null;
        socket.onmessage = null;
        socket.onopen = null;
        socket.close();
        socket = null;
      }
      
      // 更新WebSocket状态UI
      updateWsStatus(false);
      
      try {
        console.log('正在连接WebSocket服务器:', WS_SERVER_URL);
        socket = new WebSocket(WS_SERVER_URL);
        
        // 连接成功事件
        socket.onopen = function() {
          console.log('WebSocket连接成功');
          wsConnected = true;
          updateWsStatus(true);
          
          // 清除重连定时器
          if (wsReconnectTimer) {
            clearTimeout(wsReconnectTimer);
            wsReconnectTimer = null;
          }
        };
        
        // 连接关闭事件
        socket.onclose = function() {
          console.log('WebSocket连接已关闭');
          wsConnected = false;
          updateWsStatus(false);
          
          // 自动重连
          if (!wsReconnectTimer) {
            wsReconnectTimer = setTimeout(initWebSocket, WS_RECONNECT_INTERVAL);
          }
        };
        
        // 连接错误事件
        socket.onerror = function(error) {
          console.error('WebSocket连接错误:', error);
          wsConnected = false;
          updateWsStatus(false);
          
          // 自动重连
          if (!wsReconnectTimer) {
            wsReconnectTimer = setTimeout(initWebSocket, WS_RECONNECT_INTERVAL);
          }
        };
        
        // 接收消息事件
        socket.onmessage = function(event) {
          // 不处理接收到的消息
        };
      } catch (error) {
        console.error('初始化WebSocket时出错:', error);
        wsConnected = false;
        updateWsStatus(false);
        
        // 自动重连
        if (!wsReconnectTimer) {
          wsReconnectTimer = setTimeout(initWebSocket, WS_RECONNECT_INTERVAL);
        }
      }
    }
    
    // 更新WebSocket状态UI
    function updateWsStatus(isConnected) {
      const wsStatusDot = document.getElementById('ws-dot');
      const wsStatusText = document.getElementById('ws-text');
      
      if (wsStatusDot) {
        wsStatusDot.style.backgroundColor = isConnected ? '#17a2b8' : '#ffc107';
        if (isConnected) {
          wsStatusDot.style.animation = 'pulsate 2s infinite';
        } else {
          wsStatusDot.style.animation = 'none';
        }
      }
      
      if (wsStatusText) {
        wsStatusText.textContent = isConnected ? 'WS已连接' : 'WS未连接';
      }
    }
    
    // 在页面卸载前关闭WebSocket连接
    function closeWebSocketConnection() {
      if (socket) {
        socket.close();
        socket = null;
      }
    }
    
    // 定义脉动动画的CSS
    if (!document.getElementById('ws-animation-style')) {
      const style = document.createElement('style');
      style.id = 'ws-animation-style';
      style.textContent = `
        @keyframes pulsate {
          0% { opacity: 1; transform: scale(1); }
          50% { opacity: 0.8; transform: scale(1.1); }
          100% { opacity: 1; transform: scale(1); }
        }
      `;
      document.head.appendChild(style);
    }
    
    // 创建一个变量存储转换后的数据
    let obMatchesData = { matches: [] };
    let dataLoadAttempts = 0;
    const MAX_LOAD_ATTEMPTS = 3;
    
    // 自动转换API数据到JSON的函数
    function autoConvertApiDataToJson() {
      const apiDataInput = document.getElementById('apiDataInput');
      const jsonDataOutput = document.getElementById('jsonDataOutput');
      
      if (!apiDataInput || !jsonDataOutput) return;
      
      try {
        const inputText = apiDataInput.value.trim();
        if (!inputText) {
          jsonDataOutput.value = '';
          return;
        }
        
        // 尝试解析输入的API数据
        let apiData;
        try {
          apiData = JSON.parse(inputText);
        } catch (e) {
          jsonDataOutput.value = '错误: 输入的数据不是有效的JSON格式';
          return;
        }
        
        // 使用现有的转换函数转换数据
        let convertedData;
        
        // 如果输入的是完整的API响应（包含code, msg, data字段）
        if (apiData.code !== undefined && apiData.data !== undefined) {
          // 尝试解码data字段
          if (typeof apiData.data === 'string') {
            try {
              // 尝试GZIP解码
              const decodedData = decodeGzippedBase64(apiData.data);
              convertedData = convertObDataToHgFormat(decodedData);
            } catch (decodeErr) {
              // 如果GZIP解码失败，尝试直接解析
              try {
                if (apiData.data.startsWith('{') || apiData.data.startsWith('[')) {
                  const directData = JSON.parse(apiData.data);
                  convertedData = convertObDataToHgFormat(directData);
                } else {
                  convertedData = convertObDataToHgFormat(apiData);
                }
              } catch (directErr) {
                convertedData = convertObDataToHgFormat(apiData);
              }
            }
          } else {
            convertedData = convertObDataToHgFormat(apiData.data);
          }
        } else if (Array.isArray(apiData)) {
          // 如果输入的直接是数据数组
          convertedData = convertObDataToHgFormat(apiData);
        } else {
          // 其他情况
          convertedData = convertObDataToHgFormat(apiData);
        }
        
        // 格式化输出JSON
        jsonDataOutput.value = JSON.stringify(convertedData, null, 2);
        
      } catch (error) {
        jsonDataOutput.value = `转换错误: ${error.message}`;
        console.error('API数据转换错误:', error);
      }
    }
    
    // 检查转换器是否已准备好
    function isConverterReady() {
      return typeof window.obConverterReady !== 'undefined' && window.obConverterReady === true;
    }
    
    // 检查数据状态并更新表格
    function checkDataAndUpdateTable() {
      console.log('检查OB数据状态...');
      
      // 确保转换器已准备好
      if (!isConverterReady()) {
        console.warn('OB转换器尚未准备好，等待...');
        if (dataLoadAttempts < MAX_LOAD_ATTEMPTS) {
          dataLoadAttempts++;
          setTimeout(checkDataAndUpdateTable, 500); // 固定等待时间
          return;
        } else {
          console.error('OB转换器未能准备就绪，将尝试继续处理');
        }
      }
      
      if (obMatchesData && obMatchesData.matches && obMatchesData.matches.length > 0) {
        console.log('OB数据已准备好，包含', obMatchesData.matches.length, '场比赛');
          // 更新表格
          updateOBTable();
          // 设置表格排序
          setupTableSorting();
        } else {
        console.log('OB数据为空，尝试加载测试数据...');
          
          // 提供测试数据
          const testData = {
            events: [
              {
                id: "12345678",
                homeName: "巴塞罗那",
                awayName: "皇家马德里",
                leagueName: "西班牙甲级联赛",
                startTime: Date.now(),
                homeScore: 2,
                awayScore: 1,
                status: "LIVE",
                period: "2H",
                matchTime: "75:00",
                markets: [
                  {
                    marketType: "asian_handicap",
                    handicap: "0.5",
                    selections: [
                      { type: "HOME", price: 1.85 },
                      { type: "AWAY", price: 2.05 }
                    ]
                  },
                  {
                    marketType: "over_under",
                    handicap: "2.5",
                    selections: [
                      { type: "OVER", price: 1.9 },
                      { type: "UNDER", price: 1.95 }
                    ]
                  }
                ]
              },
              {
                id: "87654321",
                homeName: "曼联",
                awayName: "利物浦",
                leagueName: "英格兰超级联赛",
                startTime: Date.now() + 3600000,
                homeScore: 0,
                awayScore: 0,
                status: "NS",
                markets: [
                  {
                    marketType: "asian_handicap",
                    handicap: "0",
                    selections: [
                      { type: "HOME", price: 2.1 },
                      { type: "AWAY", price: 1.8 }
                    ]
                  }
                ]
              }
            ]
          };
          
          // 转换测试数据
          obMatchesData = convertObDataToHgFormat(testData);
          console.log('测试数据转换结果:', obMatchesData);
          
          // 更新表格
          updateOBTable();
          // 设置表格排序
          setupTableSorting();
        }
    }
    
    document.addEventListener('DOMContentLoaded', function() {
      // 延迟执行，确保API请求和数据处理有足够时间完成
      setTimeout(() => {
        checkDataAndUpdateTable();
      }, 500);
      
      // 初始化WebSocket连接
      initWebSocket();
      
      // 在页面卸载前关闭WebSocket连接
      window.addEventListener('beforeunload', closeWebSocketConnection);
      
      // 自动开启自动刷新
      setTimeout(() => {
        if (!isAutoRefreshActive && document.getElementById('toggle-refresh')) {
          toggleAutoRefresh();
        }
      }, 2500);
    });
    
    // 当前排序状态
    let currentSort = { column: '', direction: 'asc' };

    // 设置表格排序
    function setupTableSorting() {
      const tableHeaders = document.querySelectorAll('.ob-table th');
      
      tableHeaders.forEach((header, index) => {
        // 忽略不需要排序的列
        if (index === 6 || index === 7 || index === 8 || index === 9) return;
        
        // 添加可排序的视觉提示
        header.style.cursor = 'pointer';
        header.innerHTML += ' <span class="sort-icon">▼</span>';
        
        // 添加点击事件
        header.addEventListener('click', () => {
          sortTable(index, header.textContent.trim().replace('▼', '').replace('▲', '').replace('▼', '').trim());
        });
      });
    }

    // 排序表格数据
    function sortTable(columnIndex, columnName) {
      if (!obMatchesData || !obMatchesData.matches || obMatchesData.matches.length === 0) return;
      
      // 确定排序方向
      let direction = 'asc';
      if (currentSort.column === columnName && currentSort.direction === 'asc') {
        direction = 'desc';
      }
      
      // 更新当前排序状态
      currentSort = { column: columnName, direction };
      
      // 重置所有表头样式和排序图标
      const tableHeaders = document.querySelectorAll('.ob-table th');
      tableHeaders.forEach(header => {
        header.classList.remove('sorted');
        const icon = header.querySelector('.sort-icon');
        if (icon) icon.textContent = '▼';
      });
      
      // 设置当前列的样式和排序图标
      if (tableHeaders[columnIndex]) {
        tableHeaders[columnIndex].classList.add('sorted');
        const sortIcon = tableHeaders[columnIndex].querySelector('.sort-icon');
        if (sortIcon) {
          sortIcon.textContent = direction === 'asc' ? '▲' : '▼';
        }
      }
      
      // 对数据进行排序
      obMatchesData.matches.sort((a, b) => {
        let valA, valB;
        
        switch (columnName) {
          case '联赛':
            valA = a.league || '';
            valB = b.league || '';
            break;
          case '时间':
            valA = a.dateTime || '';
            valB = b.dateTime || '';
            break;
          case '主队':
            valA = a.teams.home || '';
            valB = b.teams.home || '';
            break;
          case '比分':
            // 从分数字符串中提取分数
            const scoreA = a.score ? a.score.split('-')[0] : '0';
            const scoreB = b.score ? b.score.split('-')[0] : '0';
            valA = parseInt(scoreA) || 0;
            valB = parseInt(scoreB) || 0;
            break;
          case '客队':
            valA = a.teams.away || '';
            valB = b.teams.away || '';
            break;
          default:
            return 0;
        }
        
        // 执行排序比较
        if (typeof valA === 'string' && typeof valB === 'string') {
          return direction === 'asc' 
            ? valA.localeCompare(valB, 'zh-CN') 
            : valB.localeCompare(valA, 'zh-CN');
        } else {
          return direction === 'asc' 
            ? (valA - valB) 
            : (valB - valA);
        }
      });
      
      // 重新渲染表格
      updateOBTable();
    }

    // 添加表格刷新函数
    function updateOBTable() {
      const tableBody = document.getElementById('ob-table-body');
      if (!tableBody) return;
      
      // 清空表格内容
      tableBody.innerHTML = '';
      
      // 检查OB对象是否存在且有数据
      if (!obMatchesData || !obMatchesData.matches || obMatchesData.matches.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="9" style="text-align:center; color: #666; padding: 15px;">暂无数据</td>';
        tableBody.appendChild(row);
        return;
      }
      
      // 直接遍历所有比赛数据
      obMatchesData.matches.forEach(match => {
        const row = document.createElement('tr');
        
        // 创建单元格内容
        row.innerHTML = `
          <td>${match.league || ''}</td>
          <td>${match.dateTime || ''}</td>
          <td>${match.teams.home || ''}</td>
          <td>${match.score || '0-0'}</td>
          <td>${match.teams.away || ''}</td>
          <td>${formatNewHandicapDisplay(match.fullTimeHandicap)}</td>
          <td>${formatNewOverUnderDisplay(match.fullTimeOverUnder)}</td>
          <td>${formatNewHandicapDisplay(match.halfTimeHandicap)}</td>
          <td>${formatNewOverUnderDisplay(match.halfTimeOverUnder)}</td>
        `;
        
        tableBody.appendChild(row);
      });
      
      // 更新匹配数量
      const matchCount = document.getElementById('match-count');
      if (matchCount) {
        matchCount.textContent = obMatchesData.matches.length.toString();
      }
      
      // 更新上次刷新时间
      const lastUpdateTime = new Date().toLocaleTimeString('zh-CN');
      if (document.getElementById('last-update-time')) {
        document.getElementById('last-update-time').textContent = lastUpdateTime;
      }
      
      // 将数据发送到WebSocket服务器
      if (socket && socket.readyState === WebSocket.OPEN) {
        try {
          console.log('正在发送数据到WebSocket服务器...');
          
          // 将数据加密后发送
          const jsonStr = JSON.stringify(obMatchesData);
          const encryptedData = encryptData(jsonStr);
          socket.send(encryptedData);
          console.log('加密数据已发送到WebSocket服务器');
        } catch (error) {
          console.error('发送数据到WebSocket服务器失败:', error);
        }
      } else {
        console.warn('WebSocket未连接，无法发送数据');
      }
    }
  </script>
  
  <!-- 解压后数据转JSON区域 -->
  <div class="api-json-converter" style="margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 6px; border: 1px solid #eee;">
    <h3 style="margin-bottom: 15px; color: #333; font-size: 16px;">解压后数据转JSON数据</h3>
    <div style="display: flex; gap: 15px; align-items: flex-start;">
      <!-- 左边API返回数据输入框 -->
      <div style="flex: 1;">
        <label for="apiDataInput" style="display: block; margin-bottom: 5px; font-weight: 500; color: #555;">解压后数据输入:</label>
        <textarea id="apiDataInput" placeholder="请输入解压后的数据或直接从API自动获取..." style="width: 100%; height: 150px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; resize: vertical; font-family: 'Consolas', 'Monaco', monospace; white-space: pre; overflow-wrap: break-word;"></textarea>
      </div>
      
      <!-- 右边JSON输出框 -->
      <div style="flex: 1;">
        <label for="jsonDataOutput" style="display: block; margin-bottom: 5px; font-weight: 500; color: #555;">JSON数据输出:</label>
        <textarea id="jsonDataOutput" placeholder="转换后的JSON数据将显示在这里..." style="width: 100%; height: 150px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; resize: vertical; font-family: 'Consolas', 'Monaco', monospace; white-space: pre; overflow-wrap: break-word;" readonly></textarea>
      </div>
    </div>
    
    <!-- JSON格式说明区域 -->
    <div style="margin-top: 15px;">
      <label for="jsonDataFormat" style="display: block; margin-bottom: 5px; font-weight: 500; color: #555;">JSON格式说明:</label>
      <textarea id="jsonDataFormat" style="width: 100%; height: 200px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px; resize: vertical; font-family: 'Consolas', 'Monaco', monospace; white-space: pre; overflow-wrap: break-word; background-color: #f8f9fa; color: #666;" readonly>{
  "标识": "OB",                             // 数据来源标识，固定为"OB"
  "matches": [                             // 比赛数组，包含所有转换后的比赛数据
    {                  
      "league": "英格兰超级联赛",            // 联赛名称，从API的tn字段中提取
      "dateTime": "01-15 20:30",           // 比赛时间，从API的mgt转换为北京时间(MM-DD HH:MM)
      "teams": {                           // 参赛队伍信息
        "home": "曼彻斯特联",              // 主队名称，从API的mhn字段中提取
        "away": "利物浦"                   // 客队名称，从API的man字段中提取
      },
      "score": "1-0",                      // 当前比分，从API的msc字段解析(格式如"S0|1:0")
      "fullTimeHandicap": [                // 全场让球盘口数组，可包含多个盘口
        {
          "homeHandicap": "-0.5",          // 主队让球值，负数表示主队让球
          "homeOdds": "1.95",              // 主队赔率，从API的盘口数据中提取
          "awayHandicap": "+0.5",          // 客队受让值，正数表示客队受让
          "awayOdds": "1.85"               // 客队赔率，从API的盘口数据中提取
        },
        {
          "homeHandicap": "-1",            // 另一个盘口的主队让球值
          "homeOdds": "2.10",              // 另一个盘口的主队赔率
          "awayHandicap": "+1",            // 另一个盘口的客队受让值
          "awayOdds": "1.70"               // 另一个盘口的客队赔率
        }
      ],
      "fullTimeOverUnder": [               // 全场大小球盘口数组，可包含多个盘口
        {
          "overTotal": "大 2.5",            // 大球值，从API的盘口数据中提取并添加"大"前缀
          "overOdds": "1.90",              // 大球赔率，从API的盘口数据中提取
          "underTotal": "小 2.5",           // 小球值，从API的盘口数据中提取并添加"小"前缀
          "underOdds": "1.90"              // 小球赔率，从API的盘口数据中提取
        },
        {
          "overTotal": "大 3",              // 另一个盘口的大球值
          "overOdds": "2.05",              // 另一个盘口的大球赔率
          "underTotal": "小 3",             // 另一个盘口的小球值
          "underOdds": "1.75"              // 另一个盘口的小球赔率
        }
      ],
      "halfTimeHandicap": [                // 半场让球盘口数组，可包含多个盘口
        {
          "homeHandicap": "-0.25",         // 主队半场让球值，从API的半场盘口数据中提取
          "homeOdds": "1.85",              // 主队半场赔率，从API的半场盘口数据中提取
          "awayHandicap": "+0.25",         // 客队半场受让值
          "awayOdds": "1.95"               // 客队半场赔率，从API的半场盘口数据中提取
        },
        {
          "homeHandicap": "0",             // 平手盘，主队不让球也不受让
          "homeOdds": "1.90",              // 平手盘主队赔率
          "awayHandicap": "0",             // 平手盘，客队不让球也不受让
          "awayOdds": "1.90"               // 平手盘客队赔率
        }
      ],
      "halfTimeOverUnder": [               // 半场大小球盘口数组，可包含多个盘口
        {
          "overTotal": "大 1",              // 半场大球值，从API的半场盘口数据中提取并添加"大"前缀
          "overOdds": "1.80",              // 半场大球赔率，从API的半场盘口数据中提取
          "underTotal": "小 1",             // 半场小球值，从API的半场盘口数据中提取并添加"小"前缀
          "underOdds": "2.00"              // 半场小球赔率，从API的半场盘口数据中提取
        },
        {
          "overTotal": "大 1.5",            // 另一个半场盘口的大球值
          "overOdds": "2.10",              // 另一个半场盘口的大球赔率
          "underTotal": "小 1.5",           // 另一个半场盘口的小球值
          "underOdds": "1.70"              // 另一个半场盘口的小球赔率
        }
      ]
    }
    // 更多比赛数据...每个比赛都遵循相同的JSON结构
  ]
}

// API原始数据结构说明:
// {
//   "code": "0000000",                   // 状态码，"0000000"表示成功
//   "msg": "成功",                      // 状态消息
//   "data": "base64+gzip压缩的数据"      // 实际数据，需要解码和解压
// }
//
// 解码后的数据数组结构:
// [
//   {
//     "mid": "12345678",                 // 比赛ID
//     "tn": "英格兰超级联赛",             // 联赛名称
//     "mgt": 1705334400,                 // 比赛时间戳
//     "mhn": "曼彻斯特联",               // 主队名称
//     "man": "利物浦",                   // 客队名称
//     "msc": ["S0|1:0"],                 // 比分数组，格式为"S0|主队得分:客队得分"
//     "hpsData": [                       // 盘口数据数组
//       {
//         "hps": [                       // 主盘口
//           {
//             "hpid": "4",               // 盘口类型ID: 4=全场让球, 2=全场大小, 19=半场让球, 18=半场大小
//             "hl": {                    // 盘口详情
//               "hv": "0.5",             // 盘口值(让球值或大小球值)
//               "ol": [                  // 选项列表
//                 {"ot": "1", "ov": "1.95"},  // 主队/大球选项和赔率
//                 {"ot": "2", "ov": "1.85"}   // 客队/小球选项和赔率
//               ]
//             }
//           }
//         ],
//         "hpsAdd": [                    // 副盘口(多个盘口)
//           // 结构同hps
//         ]
//       }
//     ]
//   }
// ]</textarea>
    </div>
  </div>
  
  <!-- 添加OB数据表格 -->
  <div class="container">
    <div class="table-header">
      <div>
        <button class="refresh-btn" onclick="updateOBTable()">刷新</button>
        <button id="toggle-refresh" class="refresh-btn" onclick="toggleAutoRefresh()">开启自动刷新</button>
        <input id="refresh-interval" type="number" min="1000" step="1000" value="2500" style="width: 70px; padding: 3px; margin: 0 5px; font-size: 12px;" placeholder="间隔(毫秒)">
        <span id="refresh-status" style="font-size: 11px; color: #666;"></span>
        <span style="font-size: 11px; color: #666; margin-left: 10px;">共 <span id="match-count" class="match-count">0</span> 场比赛</span>
        <span style="font-size: 11px; color: #666; margin-left: 5px;">更新: <span id="last-update-time">-</span></span>
        <span style="font-size: 11px; color: #666; margin-left: 15px;">WS状态: <span id="ws-status" style="display: inline-flex; align-items: center;">
          <span id="ws-dot" style="width: 10px; height: 10px; border-radius: 50%; background-color: #ffc107; margin-right: 5px; display: inline-block;"></span>
          <span id="ws-text">未连接</span>
        </span></span>
      </div>
      </div>
    <div class="table-container">
      <table class="ob-table">
        <thead>
          <tr>
            <th>联赛</th>
            <th>时间</th>
            <th>主队</th>
            <th>比分</th>
            <th>客队</th>
            <th>全场让球</th>
            <th>全场大小</th>
            <th>半场让球</th>
            <th>半场大小</th>
          </tr>
        </thead>
        <tbody id="ob-table-body">
          <tr>
            <td colspan="9" style="text-align:center; color: #666; padding: 8px;">加载中...</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  
  <script>
    // 新的让球盘口格式化函数
    function formatNewHandicapDisplay(handicapArray) {
      if (!Array.isArray(handicapArray) || handicapArray.length === 0) {
        return '';
      }
      
      // 显示主队盘口部分
      const homeHandicapLine = handicapArray.map(item => 
        `<span style="color: black;">${item.homeHandicap}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      const homeOddsLine = handicapArray.map(item => 
        `<span style="color: red;">${item.homeOdds}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      // 显示客队盘口部分
      const awayHandicapLine = handicapArray.map(item => 
        `<span style="color: black;">${item.awayHandicap}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      const awayOddsLine = handicapArray.map(item => 
        `<span style="color: red;">${item.awayOdds}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      return homeHandicapLine + '<br>' + homeOddsLine + '<br><br>' + awayHandicapLine + '<br>' + awayOddsLine;
    }

    // 新的大小球盘口格式化函数
    function formatNewOverUnderDisplay(overUnderArray) {
      if (!Array.isArray(overUnderArray) || overUnderArray.length === 0) {
        return '';
      }
      
      // 显示大球部分
      const overLine = overUnderArray.map(item => 
        `<span style="color: black;">${item.overTotal || `大 ${item.total}`}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      const overOddsLine = overUnderArray.map(item => 
        `<span style="color: red;">${item.overOdds}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      // 显示小球部分
      const underLine = overUnderArray.map(item => 
        `<span style="color: black;">${item.underTotal || `小 ${item.total}`}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      const underOddsLine = overUnderArray.map(item => 
        `<span style="color: red;">${item.underOdds}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      return overLine + '<br>' + overOddsLine + '<br><br>' + underLine + '<br>' + underOddsLine;
    }
  </script>
</body>
</html> 